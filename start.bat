@echo off
echo Starting Red.ai RAG Engine with Worker...
echo.

REM Kiểm tra Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found. Please install Python first.
    pause
    exit /b 1
)

echo Starting Huey worker in background...
start "Huey Worker" cmd /k "python run_worker.py"

echo Waiting 3 seconds for worker to initialize...
timeout /t 3 /nobreak >nul

echo Starting FastAPI server...
python -m uvicorn main:app --host 0.0.0.0 --port 3000 --reload

echo.
echo Both services stopped.
pause
