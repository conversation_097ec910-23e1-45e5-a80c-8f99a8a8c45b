"""Main application entry point for Red.ai RAG Engine with integrated worker."""

import os
import sys
import time
import threading
import logging.config
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
import uvicorn

# Set UTF-8 encoding for I/O operations to handle Unicode characters properly
os.environ["PYTHONIOENCODING"] = "UTF-8"

# Check for FFmpeg and Tesseract OCR installation
import subprocess
import warnings

def check_ffmpeg():
    """Check if FFmpeg is installed."""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed."""
    try:
        subprocess.run(['tesseract', '--version'], capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

# Check for FFmpeg
ffmpeg_installed = check_ffmpeg()
if not ffmpeg_installed:
    print("WARNING: FFmpeg not found. Video/audio processing will not work.")

# Check for Tesseract OCR
tesseract_installed = check_tesseract()
if not tesseract_installed:
    print("WARNING: Tesseract OCR not found. OCR processing for image-based PDFs will not work.")

from api.endpoints import router as api_router
from api.middleware.api_key_auth import APIKeyMiddleware
from core.config import get_settings
from core.database import init_db
from utils.cleanup import ensure_temp_directory

# Configure logging
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        },
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'default',
            'stream': 'ext://sys.stdout',
        },
        'file': {
            'class': 'logging.FileHandler',
            'level': 'INFO',
            'formatter': 'detailed',
            'filename': 'app.log',
            'mode': 'a',
            'encoding': 'utf-8',
        },
    },
    'loggers': {
        '': {  # Root logger
            'level': 'INFO',
            'handlers': ['console', 'file'],
        },
        'uvicorn': {
            'level': 'INFO',
            'handlers': ['console', 'file'],
            'propagate': False,
        },
        'uvicorn.error': {
            'level': 'INFO',
            'handlers': ['console', 'file'],
            'propagate': False,
        },
        'uvicorn.access': {
            'level': 'INFO',
            'handlers': ['console', 'file'],
            'propagate': False,
        },
    },
}

logging.config.dictConfig(LOGGING_CONFIG)

logger = logging.getLogger("main")

# Get settings
settings = get_settings()

# Global worker thread
worker_thread = None
worker_shutdown_event = threading.Event()

def start_worker():
    """Start Huey worker in background thread."""
    global worker_thread
    
    def run_worker():
        try:
            logger.info("🔧 Starting Huey worker in background thread...")
            
            # Import huey instance from queue service
            from services.queue_service import huey
            from huey.consumer import Consumer
            
            logger.info(f"📊 Queue database: {huey.storage.filename}")
            logger.info("✅ Worker is ready to process tasks")
            
            # Start the consumer (worker)
            consumer = Consumer(huey)
            
            # Run worker until shutdown event is set
            while not worker_shutdown_event.is_set():
                try:
                    consumer.run(workers=1, periodic=True, initial_delay=0.1, backoff=1.15, max_delay=10.0, utc=True)
                except Exception as e:
                    if not worker_shutdown_event.is_set():
                        logger.error(f"❌ Worker error: {str(e)}")
                        time.sleep(1)  # Wait before retrying
                    
            logger.info("🛑 Worker thread stopped")
            
        except Exception as e:
            logger.error(f"❌ Error starting worker: {str(e)}")
    
    worker_thread = threading.Thread(target=run_worker, daemon=True)
    worker_thread.start()
    logger.info("✅ Worker thread started")

def stop_worker():
    """Stop Huey worker."""
    global worker_thread
    
    if worker_thread and worker_thread.is_alive():
        logger.info("🛑 Stopping worker thread...")
        worker_shutdown_event.set()
        worker_thread.join(timeout=5)
        logger.info("✅ Worker thread stopped")

# Lifespan context manager for startup and shutdown events
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup tasks
    logger.info("Starting up the application with integrated worker")

    # Ensure temp directory exists
    temp_dir = ensure_temp_directory()
    logger.info(f"Ensured temp directory exists: {temp_dir}")

    # Initialize database
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise

    # Start worker
    start_worker()
    
    # Give worker time to initialize
    time.sleep(2)

    yield

    # Shutdown tasks
    logger.info("Shutting down the application")
    stop_worker()

# Create FastAPI app with enhanced Swagger documentation
app = FastAPI(
    title="Red.ai RAG Engine",
    description="""
    Service for processing documents into embeddings for AI/RAG with integrated worker.

    ## Features

    * Process files from S3 URLs
    * Convert documents to Markdown
    * OCR processing for image-based PDFs
    * Split content into chunks
    * Generate embeddings
    * Store chunks and embeddings in PostgreSQL
    * Integrated background worker for async processing

    ## Authentication

    All API endpoints require an API key to be sent in the X-API-Key header.
    You can create a new API key by calling the POST /api/auth/keys endpoint.
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    swagger_ui_parameters={
        "defaultModelsExpandDepth": -1,
        "persistAuthorization": True,
        "tryItOutEnabled": True
    },
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add API key authentication middleware
app.add_middleware(APIKeyMiddleware)

# Add request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    
    # Log request
    logger.info(f"📥 {request.method} {request.url.path}")
    
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(f"📤 {request.method} {request.url.path} - {response.status_code} - {process_time:.3f}s")
    
    return response

# Include API routes
app.include_router(api_router, prefix="/api")

def add_security_scheme(app: FastAPI):
    """Add security scheme to OpenAPI schema."""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add security scheme
    openapi_schema["components"]["securitySchemes"] = {
        "ApiKeyAuth": {
            "type": "apiKey",
            "in": "header",
            "name": "X-API-Key",
            "description": "API key for authentication"
        }
    }

    # Apply security to all operations
    for path in openapi_schema["paths"].values():
        for operation in path.values():
            if operation.get("operationId") != "create_api_key":  # Skip API key creation endpoint
                operation["security"] = [{"ApiKeyAuth": []}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = lambda: add_security_scheme(app)

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "Red.ai RAG Engine",
        "status": "running",
        "version": "1.0.0",
        "worker_status": "integrated",
        "timestamp": int(time.time() * 1000)
    }

# Run the application if executed directly
if __name__ == "__main__":
    port = int(os.environ.get("PORT", settings.PORT))
    uvicorn.run(
        "main_with_worker:app",
        host="0.0.0.0",
        port=port,
        reload=False,  # Disable reload when running with integrated worker
        log_level="info"
    )
