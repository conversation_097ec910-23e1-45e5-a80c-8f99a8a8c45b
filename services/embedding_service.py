"""Service for creating and managing embeddings for document chunks."""

import logging
import json
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from models.document_chunks import DocumentChunk as KnowledgeDocumentChunk
from core.vector_store import get_embedding_with_source, get_embeddings_parallel
from core.config import get_settings

# Configure logger
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()


def create_embeddings_for_chunks(
        db: Session,
        chunk_ids: List[str],
        batch_size: int = 10,
        max_workers: int = 5  # Số lượng luồng x<PERSON> lý song song tối đa
) -> Dict[str, Any]:
    """
    Create embeddings for multiple document chunks with parallel processing.

    Args:
        db (Session): Database session
        chunk_ids (List[str]): List of chunk IDs to create embeddings for
        batch_size (int): Number of chunks to process in each batch
        max_workers (int): Maximum number of parallel workers

    Returns:
        Dict[str, Any]: Summary of embedding creation results

    Raises:
        Exception: If embedding creation fails
    """
    try:
        logger.info(f"Creating embeddings for {len(chunk_ids)} chunks in batches of {batch_size} with {max_workers} parallel workers")

        results = {
            "total": len(chunk_ids),
            "successful": 0,
            "failed": 0,
            "failed_ids": [],
            "gemini_count": 0,
            "jina_count": 0
        }

        # Process chunks in batches
        for i in range(0, len(chunk_ids), batch_size):
            batch_ids = chunk_ids[i:i + batch_size]
            logger.info(f"Processing batch {i // batch_size + 1} ({len(batch_ids)} chunks)")

            # Get chunks for this batch
            chunks = db.query(KnowledgeDocumentChunk).filter(KnowledgeDocumentChunk.id.in_(batch_ids)).all()
            chunk_dict = {str(chunk.id): chunk for chunk in chunks}

            # Lấy nội dung của các chunk để xử lý song song
            # (Get content of chunks for parallel processing)
            chunk_contents = []
            valid_chunk_ids = []

            for chunk_id in batch_ids:
                chunk = chunk_dict.get(chunk_id)
                if not chunk:
                    logger.warning(f"Chunk not found: {chunk_id}")
                    results["failed"] += 1
                    results["failed_ids"].append(chunk_id)
                    continue

                chunk_contents.append(chunk.content)
                valid_chunk_ids.append(chunk_id)

            # Xử lý song song để tạo embeddings
            # (Process in parallel to create embeddings)
            if chunk_contents:
                try:
                    # Sử dụng hàm xử lý song song để tạo embeddings
                    # (Use parallel processing function to create embeddings)
                    # Get file_id from first chunk for consistency
                    first_chunk = chunk_dict.get(valid_chunk_ids[0]) if valid_chunk_ids else None
                    file_id_for_consistency = first_chunk.file_id if first_chunk else None

                    embedding_results = get_embeddings_parallel(
                        texts=chunk_contents,
                        max_workers=max_workers,
                        file_id=file_id_for_consistency
                    )

                    # Cập nhật các chunk với kết quả embedding
                    # (Update chunks with embedding results)
                    for idx, (embedding, source, dimensions) in enumerate(embedding_results):
                        chunk_id = valid_chunk_ids[idx]
                        chunk = chunk_dict.get(chunk_id)

                        # Track embedding source for reporting
                        if source == 'gemini':
                            results["gemini_count"] += 1
                        elif source == 'jina':
                            results["jina_count"] += 1

                        # Convert embedding to JSON string for storage
                        embedding_json = json.dumps(embedding)

                        # Use ORM update instead of raw SQL
                        chunk.embedding = embedding_json
                        chunk.embedding_source = source
                        chunk.embedding_dimensions = dimensions
                        db.add(chunk)
                        results["successful"] += 1

                except Exception as e:
                    logger.error(f"Error in parallel embedding creation: {str(e)}")
                    # Fallback to sequential processing if parallel processing fails
                    logger.info("Falling back to sequential processing")

                    for idx, chunk_id in enumerate(valid_chunk_ids):
                        chunk = chunk_dict.get(chunk_id)
                        try:
                            # Create embedding for the chunk with intelligent fallback mechanism
                            # Pass file_id to maintain embedding consistency
                            embedding, source, dimensions = get_embedding_with_source(
                                text=chunk.content,
                                file_id=chunk.file_id
                            )

                            # Track embedding source for reporting
                            if source == 'gemini':
                                results["gemini_count"] += 1
                            elif source == 'jina':
                                results["jina_count"] += 1

                            # Convert embedding to JSON string for storage
                            embedding_json = json.dumps(embedding)

                            # Use ORM update instead of raw SQL
                            chunk.embedding = embedding_json
                            chunk.embedding_source = source
                            chunk.embedding_dimensions = dimensions
                            db.add(chunk)
                            results["successful"] += 1

                        except Exception as inner_e:
                            logger.error(f"Error creating embedding for chunk {chunk_id}: {str(inner_e)}")
                            results["failed"] += 1
                            results["failed_ids"].append(chunk_id)

            # Commit changes for this batch
            db.commit()
            logger.info(f"Committed batch {i // batch_size + 1}")

        logger.info(f"Embedding creation complete: {results['successful']} successful, "
                    f"{results['failed']} failed, "
                    f"{results['gemini_count']} Gemini, "
                    f"{results['jina_count']} Jina")
        return results

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error creating embeddings: {str(e)}")
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating embeddings: {str(e)}")
        raise


def update_chunk_embedding(
        db: Session,
        chunk_id: str,
        force_update: bool = False
) -> Optional[KnowledgeDocumentChunk]:
    """
    Update embedding for a single document chunk with fallback mechanism.

    Args:
        db (Session): Database session
        chunk_id (str): ID of the chunk to update
        force_update (bool): Whether to update even if embedding already exists

    Returns:
        Optional[KnowledgeDocumentChunk]: The updated document chunk or None if not found

    Raises:
        Exception: If embedding update fails
    """
    try:
        # Get chunk
        chunk = db.query(KnowledgeDocumentChunk).filter(KnowledgeDocumentChunk.id == chunk_id).first()
        if not chunk:
            logger.warning(f"Chunk not found: {chunk_id}")
            return None

        # Check if embedding exists using ORM
        has_embedding = chunk.embedding is not None

        if has_embedding and not force_update:
            logger.info(f"Chunk {chunk_id} already has embedding and force_update is False, skipping")
            return chunk

        # Create embedding with intelligent fallback mechanism
        # Pass file_id to maintain embedding consistency
        embedding, source, dimensions = get_embedding_with_source(
            text=chunk.content,
            file_id=chunk.file_id
        )

        # Convert embedding to JSON string for storage
        embedding_json = json.dumps(embedding)

        # Use ORM update instead of raw SQL
        chunk.embedding = embedding_json
        chunk.embedding_source = source
        chunk.embedding_dimensions = dimensions
        db.add(chunk)
        db.commit()

        # Refresh the chunk to get the updated data
        db.refresh(chunk)

        logger.info(f"Updated embedding for chunk {chunk_id} using {source} (dim: {dimensions})")
        return chunk

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error updating chunk embedding: {str(e)}")
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating chunk embedding: {str(e)}")
        raise


def create_embeddings_for_file(
        db: Session,
        knowledge_file_id: str,
        batch_size: int = 10,
        max_workers: int = 5  # Số lượng luồng xử lý song song tối đa
) -> Dict[str, Any]:
    """
    Create embeddings for all chunks of a knowledge file.

    Args:
        db (Session): Database session
        knowledge_file_id (str): ID of the knowledge file
        batch_size (int): Number of chunks to process in each batch
        max_workers (int): Maximum number of parallel workers

    Returns:
        Dict[str, Any]: Summary of embedding creation results
    """
    try:
        # Get all chunk IDs for the file
        chunk_ids = [
            str(id_tuple[0]) for id_tuple in
            db.query(KnowledgeDocumentChunk.id)
            .filter(KnowledgeDocumentChunk.knowledge_file_id == knowledge_file_id)
            .all()
        ]

        if not chunk_ids:
            logger.warning(f"No chunks found for knowledge file {knowledge_file_id}")
            return {
                "total": 0,
                "successful": 0,
                "failed": 0,
                "failed_ids": [],
                "gemini_count": 0,
                "jina_count": 0
            }

        logger.info(f"Creating embeddings for {len(chunk_ids)} chunks of knowledge file {knowledge_file_id} with {max_workers} parallel workers")

        # Create embeddings for the chunks using parallel processing
        return create_embeddings_for_chunks(db, chunk_ids, batch_size, max_workers)

    except Exception as e:
        logger.error(f"Error creating embeddings for file {knowledge_file_id}: {str(e)}")
        raise


def semantic_search(
        db: Session,
        query: str,
        knowledge_file_id: Optional[str] = None,
        limit: int = 10,
        threshold: float = 0.7
) -> List[Dict[str, Any]]:
    """
    Perform semantic search using embeddings with vector size awareness.

    Args:
        db (Session): Database session
        query (str): Search query
        knowledge_file_id (Optional[str]): Optional file ID to limit search scope
        limit (int): Maximum number of results to return
        threshold (float): Minimum similarity score (0-1)

    Returns:
        List[Dict[str, Any]]: Search results with similarity scores
    """
    try:
        # Get embedding for the query with standardized dimensions
        # All embeddings are now standardized to the same dimensions
        from core.vector_store import get_embedding_with_source
        from core.config import get_settings

        settings = get_settings()
        # For search queries, we don't have file context, so use default behavior
        query_embedding, source, dimensions = get_embedding_with_source(query)
        query_embedding_json = json.dumps(query_embedding)

        logger.info(f"Searching with {source} embedding (standardized to {dimensions} dimensions)")

        # SQL query for vector similarity search
        # All embeddings now have the same standardized dimensions
        sql_query = f"""
            SELECT
                id,
                knowledge_file_id,
                chunk_index,
                content,
                embedding_source,
                embedding_dimensions,
                1 - (embedding <=> %(query_embedding)s::vector) as similarity
            FROM document_chunks
            WHERE embedding IS NOT NULL
        """

        # Add filter for knowledge file if specified
        params = {
            "query_embedding": query_embedding_json,
            "threshold": threshold,
            "limit": limit,
            "dimensions": dimensions
        }

        if knowledge_file_id:
            sql_query += " AND knowledge_file_id = %(knowledge_file_id)s"
            params["knowledge_file_id"] = knowledge_file_id

        # Add similarity threshold and ordering
        sql_query += """
            AND 1 - (embedding <=> %(query_embedding)s::vector) >= %(threshold)s
            ORDER BY similarity DESC
            LIMIT %(limit)s
        """

        # Execute the query with parameters directly in the SQL
        # This avoids parameter binding issues
        formatted_query = sql_query.replace('%(query_embedding)s', f"'{params['query_embedding']}'")
        formatted_query = formatted_query.replace('%(dimensions)s', str(params['dimensions']))
        formatted_query = formatted_query.replace('%(threshold)s', str(params['threshold']))
        formatted_query = formatted_query.replace('%(limit)s', str(params['limit']))
        if knowledge_file_id:
            formatted_query = formatted_query.replace('%(knowledge_file_id)s', f"'{knowledge_file_id}'")

        # Execute the formatted query
        result = db.execute(text(formatted_query))

        # Process results
        search_results = []
        for row in result:
            # All embeddings now have standardized dimensions, so we can use the similarity directly
            similarity = float(row[6])  # similarity from SQL

            search_results.append({
                "id": str(row[0]),               # id
                "knowledge_file_id": str(row[1]), # knowledge_file_id
                "chunk_index": row[2],           # chunk_index
                "content": row[3],               # content
                "embedding_source": row[4],      # embedding_source
                "embedding_dimensions": row[5],  # embedding_dimensions
                "similarity": similarity
            })

        # Final sort by similarity (calculated values might be different from SQL sorting)
        search_results.sort(key=lambda x: x["similarity"], reverse=True)
        search_results = search_results[:limit]

        logger.info(f"Semantic search found {len(search_results)} results")
        return search_results

    except Exception as e:
        logger.error(f"Error performing semantic search: {str(e)}")
        raise


def cosine_similarity(vec1, vec2):
    """Calculate cosine similarity between two vectors."""
    import numpy as np

    # Convert to numpy arrays
    a = np.array(vec1)
    b = np.array(vec2)

    # Calculate cosine similarity
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)

    # Avoid division by zero
    if norm_a == 0 or norm_b == 0:
        return 0

    return dot_product / (norm_a * norm_b)