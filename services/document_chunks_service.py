"""Service for managing document chunks."""

import logging
import uuid
import json
import time
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from models.document_chunks import DocumentChunk as FileDocumentChunk
from core.config import get_settings
from services.chunk_service import split_markdown_into_chunks

# Configure logger
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()


def create_chunks_for_file(
    db: Session,
    file_id: str,
    markdown_content: str,
    chunk_size: Optional[int] = None,
    chunk_overlap: Optional[int] = None,
    metadata: Dict[str, Any] = None,
    vector_store_id: Optional[str] = None
) -> List[FileDocumentChunk]:
    """
    Create document chunks for a file.

    Args:
        db (Session): Database session
        file_id (str): ID of the file
        markdown_content (str): Markdown content to split into chunks
        chunk_size (Optional[int]): Size of each chunk in characters
        chunk_overlap (Optional[int]): Overlap between chunks in characters
        metadata (Dict[str, Any]): Additional metadata for the chunks
        vector_store_id (Optional[str]): ID of the vector store to associate chunks with

    Returns:
        List[DocumentChunk]: List of created document chunks

    Raises:
        Exception: If chunk creation fails
    """
    try:
        # Use default values from settings if not specified
        if chunk_size is None:
            chunk_size = settings.CHUNK_SIZE
        if chunk_overlap is None:
            chunk_overlap = settings.CHUNK_OVERLAP

        # Split markdown into chunks
        chunks = split_markdown_into_chunks(
            markdown_content=markdown_content,
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )

        if not chunks:
            logger.warning(f"No chunks generated for file {file_id}")
            return []

        logger.info(f"Split content into {len(chunks)} chunks for file {file_id}")

        # Default empty metadata if not provided
        if metadata is None:
            metadata = {}

        # Kiểm tra vector_store_id nếu được cung cấp
        if not vector_store_id:
            # Nếu không có vector_store_id, sử dụng giá trị mặc định
            vector_store_id = "vs_default"
            logger.info(f"Không có vector store ID, sử dụng giá trị mặc định: {vector_store_id}")
        else:
            # Kiểm tra định dạng vector_store_id (vs_XXXX)
            if not vector_store_id.startswith("vs_"):
                logger.warning(f"Vector store ID không đúng định dạng: {vector_store_id}, sử dụng giá trị mặc định")
                vector_store_id = "vs_default"
            else:
                logger.info(f"Sử dụng vector store ID: {vector_store_id}")

        # Create document chunks
        document_chunks = []
        total_content_size = 0  # Track total content size for vector store storage update

        for index, content in enumerate(chunks):
            # Calculate content size in bytes
            content_size = len(content.encode('utf-8'))
            total_content_size += content_size

            chunk = FileDocumentChunk(
                id=uuid.uuid4(),
                file_id=file_id,
                chunk_index=index,
                content=content,
                chunk_metadata=metadata,
                vector_store_id=vector_store_id
                # No embedding field - it will be added later
            )
            document_chunks.append(chunk)

        # Add all chunks to the database
        db.add_all(document_chunks)

        # Không cần cập nhật vector store storage nữa

        # Commit all changes
        db.commit()

        # Refresh to get IDs and timestamps
        for chunk in document_chunks:
            db.refresh(chunk)

        logger.info(f"Successfully saved {len(document_chunks)} chunks for file {file_id}")
        return document_chunks

    except SQLAlchemyError as e:
        db.rollback()
        logger.error(f"Database error creating chunks: {str(e)}")
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating chunks: {str(e)}")
        raise


def create_embeddings_for_chunks(
    db: Session,
    chunk_ids: List[str],
    batch_size: int = 10,
    max_workers: int = 5
) -> Dict[str, Any]:
    """
    Create embeddings for document chunks.

    Args:
        db (Session): Database session
        chunk_ids (List[str]): List of chunk IDs
        batch_size (int): Number of chunks to process in each batch
        max_workers (int): Maximum number of parallel workers

    Returns:
        Dict[str, Any]: Summary of embedding creation results

    Raises:
        Exception: If embedding creation fails
    """
    try:
        # Initialize results
        results = {
            "total": len(chunk_ids),
            "successful": 0,
            "failed": 0,
            "failed_ids": [],
            "gemini_count": 0,
            "jina_count": 0
        }

        if not chunk_ids:
            logger.warning("No chunk IDs provided for embedding creation")
            return results

        logger.info(f"Creating embeddings for {len(chunk_ids)} chunks with {max_workers} parallel workers")

        # Process chunks in batches
        for i in range(0, len(chunk_ids), batch_size):
            batch_ids = chunk_ids[i:i + batch_size]
            logger.info(f"Processing batch {i // batch_size + 1} ({len(batch_ids)} chunks)")

            # Get chunks for this batch
            chunks = db.query(FileDocumentChunk).filter(FileDocumentChunk.id.in_(batch_ids)).all()
            chunk_dict = {str(chunk.id): chunk for chunk in chunks}

            # Get content of chunks for parallel processing
            chunk_contents = []
            valid_chunk_ids = []

            for chunk_id in batch_ids:
                chunk = chunk_dict.get(chunk_id)
                if not chunk:
                    logger.warning(f"Chunk not found: {chunk_id}")
                    results["failed"] += 1
                    results["failed_ids"].append(chunk_id)
                    continue

                chunk_contents.append(chunk.content)
                valid_chunk_ids.append(chunk_id)

            if not valid_chunk_ids:
                logger.warning(f"No valid chunks found in batch")
                continue

            try:
                # Create embeddings in parallel
                logger.info(f"Creating embeddings for {len(chunk_contents)} chunks in parallel")
                from core.vector_store import get_embeddings_parallel
                embedding_results = get_embeddings_parallel(
                    texts=chunk_contents,
                    max_workers=max_workers
                )
                logger.info(f"Parallel embedding creation completed, got {len(embedding_results)} results")

                # Track vector store storage updates
                vector_store_updates = {}

                # Update chunks with embedding results
                for idx, (embedding, source, dimensions) in enumerate(embedding_results):
                    chunk_id = valid_chunk_ids[idx]
                    chunk = chunk_dict.get(chunk_id)

                    # Skip failed embeddings
                    if source == 'failed' or not embedding:
                        logger.error(f"Failed to create embedding for chunk {chunk_id}")
                        results["failed"] += 1
                        results["failed_ids"].append(chunk_id)
                        continue

                    # Track embedding source for reporting
                    if source == 'gemini':
                        results["gemini_count"] += 1
                    elif source == 'jina':
                        results["jina_count"] += 1

                    # Convert embedding to JSON string for storage
                    embedding_json = json.dumps(embedding)

                    # Calculate storage size (approximate size of the embedding JSON)
                    storage_size = len(embedding_json.encode('utf-8'))

                    # Update chunk
                    chunk.embedding = embedding_json
                    chunk.embedding_source = source
                    chunk.embedding_dimensions = dimensions
                    db.add(chunk)
                    results["successful"] += 1

                    logger.info(f"Updated chunk {chunk_id} with {source} embedding ({dimensions} dims, {storage_size} bytes)")

                    # Không cần theo dõi vector store storage nữa

                # Commit changes for chunks
                db.commit()

                # Không cần cập nhật vector store storage nữa

            except Exception as e:
                db.rollback()
                logger.error(f"Error creating embeddings in parallel: {str(e)}")

                # Fall back to sequential processing
                logger.info("Falling back to sequential embedding creation")

                for idx, chunk_id in enumerate(valid_chunk_ids):
                    chunk = chunk_dict.get(chunk_id)
                    try:
                        # Create embedding for the chunk
                        from core.vector_store import get_embedding_with_source
                        embedding, source, dimensions = get_embedding_with_source(chunk.content)

                        # Track embedding source
                        if source == 'gemini':
                            results["gemini_count"] += 1
                        elif source == 'jina':
                            results["jina_count"] += 1

                        # Convert embedding to JSON string
                        embedding_json = json.dumps(embedding)

                        # Calculate storage size
                        storage_size = len(embedding_json.encode('utf-8'))

                        # Update chunk
                        chunk.embedding = embedding_json
                        chunk.embedding_source = source
                        chunk.embedding_dimensions = dimensions
                        db.add(chunk)

                        # Không cần cập nhật vector store storage nữa

                        db.commit()
                        results["successful"] += 1

                    except Exception as chunk_error:
                        db.rollback()
                        logger.error(f"Error creating embedding for chunk {chunk_id}: {str(chunk_error)}")
                        results["failed"] += 1
                        results["failed_ids"].append(chunk_id)

        return results

    except Exception as e:
        logger.error(f"Error creating embeddings: {str(e)}")
        raise


def get_chunks_for_file(
    db: Session,
    file_id: str,
    skip: int = 0,
    limit: int = 100
) -> List[Dict[str, Any]]:
    """
    Get chunks for a file.

    Args:
        db (Session): Database session
        file_id (str): ID of the file
        skip (int): Number of records to skip (pagination)
        limit (int): Maximum number of records to return

    Returns:
        List[Dict[str, Any]]: List of document chunks
    """
    try:
        chunks = db.query(FileDocumentChunk) \
            .filter(FileDocumentChunk.file_id == file_id) \
            .order_by(FileDocumentChunk.chunk_index) \
            .offset(skip) \
            .limit(limit) \
            .all()

        # Convert chunks to dictionaries
        result = []
        for chunk in chunks:
            chunk_dict = {
                "id": chunk.id,
                "file_id": chunk.file_id,
                "chunk_index": chunk.chunk_index,
                "content": chunk.content,
                "metadata": chunk.chunk_metadata or {},
                "created_at": chunk.created_at,
                "updated_at": chunk.updated_at,
                "embedding_source": chunk.embedding_source,
                "embedding_dimensions": chunk.embedding_dimensions
            }
            result.append(chunk_dict)

        logger.info(f"Retrieved {len(chunks)} chunks for file {file_id}")
        return result

    except Exception as e:
        logger.error(f"Error getting chunks for file {file_id}: {str(e)}")
        raise
