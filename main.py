"""Main application entry point for Red.ai RAG Engine."""

import os
import sys
import time
import logging.config
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
import uvicorn

# Set UTF-8 encoding for I/O operations to handle Unicode characters properly
os.environ["PYTHONIOENCODING"] = "UTF-8"

# Check for FFmpeg and Tesseract OCR installation
import subprocess
import warnings

def check_ffmpeg():
    """Check if FFmpeg is installed and suppress warnings if not."""
    try:
        subprocess.run(["ffmpeg", "-version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        return True
    except FileNotFoundError:
        warnings.filterwarnings("ignore", category=RuntimeWarning, message="Couldn't find ffmpeg or avconv")
        return False

def check_tesseract():
    """Check if Tesseract OCR is installed."""
    try:
        subprocess.run(["tesseract", "--version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
        return True
    except FileNotFoundError:
        return False

# Suppress FFmpeg warnings if not installed
ffmpeg_installed = check_ffmpeg()
if not ffmpeg_installed:
    print("WARNING: FFmpeg not found. Some PDF processing features may be limited.")

# Check for Tesseract OCR
tesseract_installed = check_tesseract()
if not tesseract_installed:
    print("WARNING: Tesseract OCR not found. OCR processing for image-based PDFs will not work.")

from api.endpoints import router as api_router
from api.middleware.api_key_auth import APIKeyMiddleware
from core.config import get_settings
from core.database import init_db
from utils.cleanup import ensure_temp_directory

# Configure logging - Terminal only
logging.config.dictConfig({
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "detailed": {
            "format": "%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "detailed",
            "stream": "ext://sys.stdout"
        }
    },
    "loggers": {
        "": {  # Root logger
            "handlers": ["console"],
            "level": "INFO"
        }
    }
})

logger = logging.getLogger("main")

# Get settings
settings = get_settings()

# Lifespan context manager for startup and shutdown events
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup tasks
    logger.info("Starting up the application")

    # Ensure temp directory exists
    temp_dir = ensure_temp_directory()
    logger.info(f"Ensured temp directory exists: {temp_dir}")

    # Initialize database
    try:
        init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise

    yield

    # Shutdown tasks
    logger.info("Shutting down the application")

# Create FastAPI app with enhanced Swagger documentation
app = FastAPI(
    title="Red.ai RAG Engine",
    description="""
    Service for processing documents into embeddings for AI/RAG.

    ## Features

    * Process files from S3 URLs
    * Convert documents to Markdown
    * OCR processing for image-based PDFs
    * Split content into chunks
    * Generate embeddings
    * Store chunks and embeddings in PostgreSQL

    ## Authentication

    All API endpoints require an API key to be sent in the X-API-Key header.
    You can create a new API key by calling the POST /api/auth/keys endpoint.
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    swagger_ui_parameters={
        "defaultModelsExpandDepth": -1,
        "persistAuthorization": True,
        "tryItOutEnabled": True
    },
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify allowed origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add API key authentication middleware
app.add_middleware(APIKeyMiddleware)

# Add middleware to log request processing time
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Middleware to log request processing time."""
    path = request.url.path
    start_time = time.time()

    try:
        response = await call_next(request)
    except Exception as e:
        logger.error(f"Error processing request to {path}: {str(e)}")
        raise
    finally:
        process_time = time.time() - start_time

    response.headers["X-Process-Time"] = str(process_time)
    logger.info(f"Request to {path} completed in {process_time:.4f}s")

    return response

# Security scheme for Swagger UI is added via OpenAPI schema

# Include API router
app.include_router(api_router)

# Add security scheme to OpenAPI
app.openapi_tags = [
    {"name": "auth", "description": "Authentication operations"},
    {"name": "files", "description": "File processing operations"}
]

# Add security scheme
def add_security_scheme(app):
    """Add API key security scheme to OpenAPI schema."""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
        tags=app.openapi_tags
    )

    # Add API key security scheme
    openapi_schema["components"] = openapi_schema.get("components", {})
    openapi_schema["components"]["securitySchemes"] = {
        "ApiKeyAuth": {
            "type": "apiKey",
            "in": "header",
            "name": "X-API-Key",
            "description": "API key for authentication"
        }
    }

    # Apply security to all operations
    for path in openapi_schema["paths"].values():
        for operation in path.values():
            if operation.get("operationId") != "create_api_key":  # Skip API key creation endpoint
                operation["security"] = [{"ApiKeyAuth": []}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = lambda: add_security_scheme(app)

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "service": "Red.ai RAG Engine",
        "status": "running",
        "version": "1.0.0",
        "timestamp": int(time.time() * 1000)
    }

# Run the application if executed directly
if __name__ == "__main__":
    port = int(os.environ.get("PORT", settings.PORT))
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )