#!/usr/bin/env python3
"""
Test script for real database consistency with embedding logic.
"""

import logging
import sys
import os
import uuid

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_real_database_consistency():
    """Test consistency with real database operations."""
    try:
        from core.database import get_db_session
        from models.file import File
        from models.document_chunks import DocumentChunk
        from core.vector_store import get_embedding_with_source, get_file_embedding_source
        import time
        import json
        
        logger.info("🧪 Testing real database consistency...")
        
        # Create a test file
        test_file_id = f"test-file-{uuid.uuid4().hex[:8]}"
        db = get_db_session()
        
        try:
            # Create test file
            test_file = File(
                id=test_file_id,
                filename="test_consistency.txt",
                filepath="https://example.com/test.txt",
                filesize=1000,
                uploaded_at=int(time.time() * 1000)
            )
            db.add(test_file)
            db.commit()
            logger.info(f"✅ Created test file: {test_file_id}")
            
            # Test 1: First chunk embedding
            logger.info("\n📝 Test 1: First chunk embedding")
            embedding1, source1, dimensions1 = get_embedding_with_source(
                "This is the first chunk content for consistency test.",
                file_id=test_file_id
            )
            
            # Save first chunk to database
            chunk1 = DocumentChunk(
                id=uuid.uuid4(),
                file_id=test_file_id,
                chunk_index=0,
                content="This is the first chunk content for consistency test.",
                embedding=json.dumps(embedding1),
                embedding_source=source1,
                embedding_dimensions=dimensions1,
                vector_store_id="vs_test"
            )
            db.add(chunk1)
            db.commit()
            logger.info(f"✅ First chunk: source={source1}, saved to database")
            
            # Test 2: Check file embedding source
            logger.info("\n📝 Test 2: Check file embedding source")
            detected_source = get_file_embedding_source(test_file_id)
            logger.info(f"✅ Detected source: {detected_source}")
            
            if detected_source == source1:
                logger.info(f"✅ SOURCE DETECTION CORRECT: {detected_source}")
            else:
                logger.error(f"❌ SOURCE DETECTION FAILED: Expected {source1}, got {detected_source}")
                return False
            
            # Test 3: Second chunk should use same source
            logger.info("\n📝 Test 3: Second chunk (should use same source)")
            embedding2, source2, dimensions2 = get_embedding_with_source(
                "This is the second chunk content for consistency test.",
                file_id=test_file_id
            )
            
            # Save second chunk to database
            chunk2 = DocumentChunk(
                id=uuid.uuid4(),
                file_id=test_file_id,
                chunk_index=1,
                content="This is the second chunk content for consistency test.",
                embedding=json.dumps(embedding2),
                embedding_source=source2,
                embedding_dimensions=dimensions2,
                vector_store_id="vs_test"
            )
            db.add(chunk2)
            db.commit()
            logger.info(f"✅ Second chunk: source={source2}, saved to database")
            
            # Test 4: Consistency check
            logger.info("\n📝 Test 4: Consistency check")
            if source1 == source2:
                logger.info(f"✅ CONSISTENCY CHECK PASSED: Both chunks use {source1}")
                return True
            else:
                logger.error(f"❌ CONSISTENCY CHECK FAILED: First={source1}, Second={source2}")
                return False
                
        finally:
            # Cleanup: Remove test data
            try:
                db.query(DocumentChunk).filter(DocumentChunk.file_id == test_file_id).delete()
                db.query(File).filter(File.id == test_file_id).delete()
                db.commit()
                logger.info(f"✅ Cleaned up test data for {test_file_id}")
            except Exception as cleanup_error:
                logger.warning(f"⚠️ Cleanup error: {cleanup_error}")
            finally:
                db.close()
        
    except Exception as e:
        logger.error(f"❌ Test error: {str(e)}")
        return False

def main():
    """Main test function."""
    logger.info("🚀 Starting real database consistency test...")
    
    if test_real_database_consistency():
        logger.info("\n✅ Real database consistency test PASSED!")
        return 0
    else:
        logger.error("\n❌ Real database consistency test FAILED!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
