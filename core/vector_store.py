"""Vector store and embedding functionality for document chunks."""

import logging
import json
import time
import concurrent.futures
from typing import List, Tu<PERSON>, Dict, Any, Optional
import numpy as np

# Import embedding providers
import google.generativeai as genai
import requests

from core.config import get_settings

# Configure logger
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Configure Gemini API
genai.configure(api_key=settings.GEMINI_API_KEY)


def standardize_embedding(embedding: List[float], target_dimensions: int) -> List[float]:
    """
    Standardize embedding vector to the target dimensions by padding or truncating.

    Args:
        embedding (List[float]): Original embedding vector
        target_dimensions (int): Target dimensions for the standardized vector

    Returns:
        List[float]: Standardized embedding vector with target dimensions
    """
    current_dimensions = len(embedding)

    # If dimensions match, return as is
    if current_dimensions == target_dimensions:
        return embedding

    # If embedding is larger than target, truncate
    if current_dimensions > target_dimensions:
        logger.info(f"Truncating embedding from {current_dimensions} to {target_dimensions} dimensions")
        return embedding[:target_dimensions]

    # If embedding is smaller than target, pad with zeros
    if current_dimensions < target_dimensions:
        logger.info(f"Padding embedding from {current_dimensions} to {target_dimensions} dimensions")
        padding = [0.0] * (target_dimensions - current_dimensions)
        return embedding + padding


def get_gemini_embedding(text: str) -> List[float]:
    """
    Get embedding from Google Gemini API and standardize to target dimensions.
    Updated to use the new Gemini API (google-generativeai >= 0.8.0).

    Args:
        text (str): Text to embed

    Returns:
        List[float]: Standardized embedding vector

    Raises:
        Exception: If embedding creation fails
    """
    try:
        # Truncate text if too long (Gemini has a token limit)
        if len(text) > 25000:
            logger.warning(f"Text too long for Gemini embedding ({len(text)} chars), truncating to 25000 chars")
            text = text[:25000]

        # Get embedding from Gemini using the new API
        result = genai.embed_content(
            model=settings.GEMINI_EMBEDDING_MODEL,
            content=text,
            task_type="SEMANTIC_SIMILARITY"
        )

        # Extract embedding values from the new response format
        embedding = result["embedding"]

        # Standardize to target dimensions
        standardized_embedding = standardize_embedding(
            embedding,
            settings.STANDARDIZED_EMBEDDING_DIMENSIONS
        )

        return standardized_embedding
    except Exception as e:
        logger.error(f"Error getting Gemini embedding: {str(e)}")
        raise


def get_jina_embedding(text: str) -> List[float]:
    """
    Get embedding from Jina AI API and standardize to target dimensions.

    Args:
        text (str): Text to embed

    Returns:
        List[float]: Standardized embedding vector

    Raises:
        Exception: If embedding creation fails
    """
    try:
        # Truncate text if too long (Jina has a token limit)
        if len(text) > 8192:
            logger.warning(f"Text too long for Jina embedding ({len(text)} chars), truncating to 8192 chars")
            text = text[:8192]

        # API endpoint
        url = "https://api.jina.ai/v1/embeddings"

        # Request headers
        headers = {
            "Authorization": f"Bearer {settings.JINA_API_KEY}",
            "Content-Type": "application/json"
        }

        # Request payload
        payload = {
            "input": text,
            "model": settings.JINA_EMBEDDING_MODEL
        }

        # Make API request
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()  # Raise exception for HTTP errors

        # Parse response
        result = response.json()

        # Extract embedding
        embedding = result["data"][0]["embedding"]

        # Standardize to target dimensions
        standardized_embedding = standardize_embedding(
            embedding,
            settings.STANDARDIZED_EMBEDDING_DIMENSIONS
        )

        return standardized_embedding
    except Exception as e:
        logger.error(f"Error getting Jina embedding: {str(e)}")
        raise


def get_embedding_with_source(text: str) -> Tuple[List[float], str, int]:
    """
    Get embedding with fallback mechanism.
    Tries Gemini first, then falls back to Jina if Gemini fails.
    All embeddings are standardized to the target dimensions.

    Args:
        text (str): Text to embed

    Returns:
        Tuple[List[float], str, int]: (embedding, source, dimensions)
        The dimensions will always be STANDARDIZED_EMBEDDING_DIMENSIONS.

    Raises:
        Exception: If all embedding methods fail
    """
    logger.info(f"Creating embedding for text (length: {len(text)} chars)")

    # Try Gemini first
    try:
        logger.info("Attempting to create embedding using Gemini...")
        embedding = get_gemini_embedding(text)
        logger.info(f"✅ Gemini embedding successful (dimensions: {len(embedding)})")
        return embedding, "gemini", settings.STANDARDIZED_EMBEDDING_DIMENSIONS
    except Exception as gemini_error:
        logger.warning(f"❌ Gemini embedding failed, falling back to Jina: {str(gemini_error)}")

        # Try Jina as fallback
        try:
            logger.info("Attempting to create embedding using Jina...")
            embedding = get_jina_embedding(text)
            logger.info(f"✅ Jina embedding successful (dimensions: {len(embedding)})")
            return embedding, "jina", settings.STANDARDIZED_EMBEDDING_DIMENSIONS
        except Exception as jina_error:
            logger.error(f"❌ All embedding methods failed. Gemini error: {str(gemini_error)}. Jina error: {str(jina_error)}")
            raise Exception(f"All embedding methods failed: {str(jina_error)}")


def get_embeddings_parallel(texts: List[str], max_workers: int = 5) -> List[Tuple[List[float], str, int]]:
    """
    Get embeddings for multiple texts in parallel.

    Args:
        texts (List[str]): List of texts to embed
        max_workers (int): Maximum number of parallel workers

    Returns:
        List[Tuple[List[float], str, int]]: List of (embedding, source, dimensions) tuples

    Raises:
        Exception: If embedding creation fails
    """
    results = []

    # Use ThreadPoolExecutor for parallel processing
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks
        future_to_text = {executor.submit(get_embedding_with_source, text): text for text in texts}

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_text):
            text = future_to_text[future]
            try:
                embedding, source, dimensions = future.result()
                results.append((embedding, source, dimensions))
            except Exception as e:
                logger.error(f"Error getting embedding for text: {str(e)}")
                # Add a placeholder for failed embedding
                results.append(([], "failed", 0))

    return results


def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """
    Calculate cosine similarity between two vectors.

    Args:
        vec1 (List[float]): First vector
        vec2 (List[float]): Second vector

    Returns:
        float: Cosine similarity (0-1)
    """
    # Convert to numpy arrays
    a = np.array(vec1)
    b = np.array(vec2)

    # Calculate cosine similarity
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)

    # Avoid division by zero
    if norm_a == 0 or norm_b == 0:
        return 0

    return dot_product / (norm_a * norm_b)
