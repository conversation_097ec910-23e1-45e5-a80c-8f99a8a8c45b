"""Vector store and embedding functionality for document chunks."""

import logging
import json
import time
import concurrent.futures
from typing import List, Tu<PERSON>, Dict, Any, Optional
import numpy as np

# Import embedding providers
import google.generativeai as genai
import requests

from core.config import get_settings

# Configure logger
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Configure Gemini API
genai.configure(api_key=settings.GEMINI_API_KEY)


def standardize_embedding(embedding: List[float], target_dimensions: int) -> List[float]:
    """
    Standardize embedding vector to the target dimensions by padding or truncating.

    Args:
        embedding (List[float]): Original embedding vector
        target_dimensions (int): Target dimensions for the standardized vector

    Returns:
        List[float]: Standardized embedding vector with target dimensions
    """
    current_dimensions = len(embedding)

    # If dimensions match, return as is
    if current_dimensions == target_dimensions:
        return embedding

    # If embedding is larger than target, truncate
    if current_dimensions > target_dimensions:
        logger.info(f"Truncating embedding from {current_dimensions} to {target_dimensions} dimensions")
        return embedding[:target_dimensions]

    # If embedding is smaller than target, pad with zeros
    if current_dimensions < target_dimensions:
        logger.info(f"Padding embedding from {current_dimensions} to {target_dimensions} dimensions")
        padding = [0.0] * (target_dimensions - current_dimensions)
        return embedding + padding


def get_gemini_embedding(text: str) -> List[float]:
    """
    Get embedding from Google Gemini API and standardize to target dimensions.
    Updated to use the new Gemini API (google-generativeai >= 0.8.0).

    Args:
        text (str): Text to embed

    Returns:
        List[float]: Standardized embedding vector

    Raises:
        Exception: If embedding creation fails
    """
    try:
        # Truncate text if too long (Gemini has a token limit)
        if len(text) > 25000:
            logger.warning(f"Text too long for Gemini embedding ({len(text)} chars), truncating to 25000 chars")
            text = text[:25000]

        # Get embedding from Gemini using the new API
        result = genai.embed_content(
            model=settings.GEMINI_EMBEDDING_MODEL,
            content=text,
            task_type="SEMANTIC_SIMILARITY"
        )

        # Extract embedding values from the new response format
        embedding = result["embedding"]

        # Standardize to target dimensions
        standardized_embedding = standardize_embedding(
            embedding,
            settings.STANDARDIZED_EMBEDDING_DIMENSIONS
        )

        return standardized_embedding
    except Exception as e:
        logger.error(f"Error getting Gemini embedding: {str(e)}")
        raise


def get_jina_embedding(text: str) -> List[float]:
    """
    Get embedding from Jina AI API and standardize to target dimensions.

    Args:
        text (str): Text to embed

    Returns:
        List[float]: Standardized embedding vector

    Raises:
        Exception: If embedding creation fails
    """
    try:
        # Truncate text if too long (Jina has a token limit)
        if len(text) > 8192:
            logger.warning(f"Text too long for Jina embedding ({len(text)} chars), truncating to 8192 chars")
            text = text[:8192]

        # API endpoint
        url = "https://api.jina.ai/v1/embeddings"

        # Request headers
        headers = {
            "Authorization": f"Bearer {settings.JINA_API_KEY}",
            "Content-Type": "application/json"
        }

        # Request payload
        payload = {
            "input": text,
            "model": settings.JINA_EMBEDDING_MODEL
        }

        # Make API request
        response = requests.post(url, headers=headers, json=payload)
        response.raise_for_status()  # Raise exception for HTTP errors

        # Parse response
        result = response.json()

        # Extract embedding
        embedding = result["data"][0]["embedding"]

        # Standardize to target dimensions
        standardized_embedding = standardize_embedding(
            embedding,
            settings.STANDARDIZED_EMBEDDING_DIMENSIONS
        )

        return standardized_embedding
    except Exception as e:
        logger.error(f"Error getting Jina embedding: {str(e)}")
        raise


def get_embedding_with_source(text: str, preferred_source: str = None, file_id: str = None) -> Tuple[List[float], str, int]:
    """
    Get embedding with intelligent fallback mechanism.

    Logic:
    1. If preferred_source is specified, try that first
    2. If no preferred_source, try Gemini first (default behavior)
    3. If Gemini fails and no embeddings exist for this file yet, fallback to Jina
    4. If Gemini fails but embeddings already exist for this file with Gemini, retry Gemini (no fallback)

    Args:
        text (str): Text to embed
        preferred_source (str): Preferred embedding source ('gemini' or 'jina')
        file_id (str): File ID to check existing embedding sources

    Returns:
        Tuple[List[float], str, int]: (embedding, source, dimensions)
        The dimensions will always be STANDARDIZED_EMBEDDING_DIMENSIONS.

    Raises:
        Exception: If all embedding methods fail
    """
    logger.info(f"Creating embedding for text (length: {len(text)} chars)")
    if preferred_source:
        logger.info(f"Preferred embedding source: {preferred_source}")
    if file_id:
        logger.info(f"File ID for context: {file_id}")

    # Determine which embedding source to try first
    if preferred_source == "jina":
        # Force Jina if explicitly requested
        try:
            logger.info("Using preferred source: Jina...")
            embedding = get_jina_embedding(text)
            logger.info(f"✅ Jina embedding successful (dimensions: {len(embedding)})")
            return embedding, "jina", settings.STANDARDIZED_EMBEDDING_DIMENSIONS
        except Exception as jina_error:
            logger.error(f"❌ Preferred Jina embedding failed: {str(jina_error)}")
            raise Exception(f"Preferred Jina embedding failed: {str(jina_error)}")

    elif preferred_source == "gemini":
        # Force Gemini if explicitly requested
        try:
            logger.info("Using preferred source: Gemini...")
            embedding = get_gemini_embedding(text)
            logger.info(f"✅ Gemini embedding successful (dimensions: {len(embedding)})")
            return embedding, "gemini", settings.STANDARDIZED_EMBEDDING_DIMENSIONS
        except Exception as gemini_error:
            logger.error(f"❌ Preferred Gemini embedding failed: {str(gemini_error)}")
            raise Exception(f"Preferred Gemini embedding failed: {str(gemini_error)}")

    else:
        # Default behavior: Try Gemini first
        try:
            logger.info("Attempting to create embedding using Gemini (default)...")
            embedding = get_gemini_embedding(text)
            logger.info(f"✅ Gemini embedding successful (dimensions: {len(embedding)})")
            return embedding, "gemini", settings.STANDARDIZED_EMBEDDING_DIMENSIONS
        except Exception as gemini_error:
            logger.warning(f"❌ Gemini embedding failed: {str(gemini_error)}")

            # Check if we should fallback to Jina or retry Gemini
            if file_id:
                existing_source = get_file_embedding_source(file_id)
                if existing_source == "gemini":
                    logger.warning(f"File {file_id} already has Gemini embeddings. Not falling back to Jina to maintain consistency.")
                    raise Exception(f"Gemini embedding failed for file with existing Gemini embeddings: {str(gemini_error)}")
                elif existing_source == "jina":
                    logger.info(f"File {file_id} already has Jina embeddings. Using Jina for consistency.")
                    try:
                        embedding = get_jina_embedding(text)
                        logger.info(f"✅ Jina embedding successful (dimensions: {len(embedding)})")
                        return embedding, "jina", settings.STANDARDIZED_EMBEDDING_DIMENSIONS
                    except Exception as jina_error:
                        logger.error(f"❌ Jina embedding also failed: {str(jina_error)}")
                        raise Exception(f"All embedding methods failed: {str(jina_error)}")
                else:
                    # No existing embeddings, safe to fallback to Jina
                    logger.info(f"No existing embeddings for file {file_id}. Falling back to Jina.")
            else:
                logger.info("No file context provided. Falling back to Jina.")

            # Try Jina as fallback
            try:
                logger.info("Attempting to create embedding using Jina...")
                embedding = get_jina_embedding(text)
                logger.info(f"✅ Jina embedding successful (dimensions: {len(embedding)})")
                return embedding, "jina", settings.STANDARDIZED_EMBEDDING_DIMENSIONS
            except Exception as jina_error:
                logger.error(f"❌ All embedding methods failed. Gemini error: {str(gemini_error)}. Jina error: {str(jina_error)}")
                raise Exception(f"All embedding methods failed: {str(jina_error)}")


def get_file_embedding_source(file_id: str) -> str:
    """
    Get the embedding source used for existing chunks of a file.

    Args:
        file_id (str): File ID to check

    Returns:
        str: 'gemini', 'jina', or 'none' if no embeddings exist
    """
    try:
        from sqlalchemy.orm import Session
        from core.database import get_db_session
        from models.document_chunks import DocumentChunk

        # Get database session
        db: Session = next(get_db_session())

        try:
            # Query for existing chunks with embeddings for this file
            chunk_with_embedding = db.query(DocumentChunk.embedding_source).filter(
                DocumentChunk.file_id == file_id,
                DocumentChunk.embedding.isnot(None),
                DocumentChunk.embedding_source.isnot(None)
            ).first()

            if chunk_with_embedding and chunk_with_embedding[0]:
                logger.info(f"Found existing embedding source for file {file_id}: {chunk_with_embedding[0]}")
                return chunk_with_embedding[0]
            else:
                logger.info(f"No existing embeddings found for file {file_id}")
                return "none"

        finally:
            db.close()

    except Exception as e:
        logger.error(f"Error checking file embedding source for {file_id}: {str(e)}")
        return "none"


def get_embeddings_parallel(texts: List[str], max_workers: int = 5, file_id: str = None) -> List[Tuple[List[float], str, int]]:
    """
    Get embeddings for multiple texts in parallel.

    Args:
        texts (List[str]): List of texts to embed
        max_workers (int): Maximum number of parallel workers
        file_id (str): File ID for embedding consistency (optional)

    Returns:
        List[Tuple[List[float], str, int]]: List of (embedding, source, dimensions) tuples

    Raises:
        Exception: If embedding creation fails
    """
    results = []

    # Use ThreadPoolExecutor for parallel processing
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all tasks with file_id for consistency
        future_to_text = {
            executor.submit(get_embedding_with_source, text, None, file_id): text
            for text in texts
        }

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_text):
            text = future_to_text[future]
            try:
                embedding, source, dimensions = future.result()
                results.append((embedding, source, dimensions))
            except Exception as e:
                logger.error(f"Error getting embedding for text: {str(e)}")
                # Add a placeholder for failed embedding
                results.append(([], "failed", 0))

    return results


def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """
    Calculate cosine similarity between two vectors.

    Args:
        vec1 (List[float]): First vector
        vec2 (List[float]): Second vector

    Returns:
        float: Cosine similarity (0-1)
    """
    # Convert to numpy arrays
    a = np.array(vec1)
    b = np.array(vec2)

    # Calculate cosine similarity
    dot_product = np.dot(a, b)
    norm_a = np.linalg.norm(a)
    norm_b = np.linalg.norm(b)

    # Avoid division by zero
    if norm_a == 0 or norm_b == 0:
        return 0

    return dot_product / (norm_a * norm_b)
