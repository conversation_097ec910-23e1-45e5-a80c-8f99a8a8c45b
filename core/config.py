"""Configuration settings for the application."""
from functools import lru_cache
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # App settings
    APP_NAME: str = "Red.ai RAG Engine"
    DEBUG: bool = False
    PORT: int = 8000

    # API Authentication settings
    API_KEY_HEADER_NAME: str = "X-API-Key"  # Header name for API key

    # Database settings
    DB_HOST: str
    DB_PORT: int
    DB_USERNAME: str
    DB_PASSWORD: str
    DB_DATABASE: str
    DB_SSL: bool

    # Combine into SQLAlchemy URL
    @property
    def DATABASE_URL(self) -> str:
        """Generate the database connection string."""
        import urllib.parse
        # URL encode the username and password to handle special characters
        username = urllib.parse.quote_plus(self.DB_USERNAME)
        password = urllib.parse.quote_plus(self.DB_PASSWORD)
        ssl_mode = "require" if self.DB_SSL else "disable"
        return f"postgresql://{username}:{password}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_DATABASE}?sslmode={ssl_mode}"

    # CloudFlare R2 Storage settings
    CF_R2_ACCESS_KEY: str
    CF_R2_SECRET_KEY: str
    CF_R2_ENDPOINT: str
    CF_BUCKET_NAME: str
    CF_R2_REGION: str

    # OpenAI API settings (deprecated, kept for backward compatibility)
    OPENAI_API_KEY: str = ""
    OPENAI_EMBEDDING_MODEL: str = "text-embedding-ada-002"
    OPENAI_EMBEDDING_DIMENSIONS: int = 1536  # Dimensions for the embedding model

    # CDN settings
    CDN_URL: str
    CDN_SECRET_KEY: str

    # Google Gemini API settings
    GEMINI_API_KEY: str
    # GEMINI_EMBEDDING_MODEL: str = "models/embedding-001"
    GEMINI_EMBEDDING_MODEL: str = "gemini-embedding-exp-03-07"
    GEMINI_EMBEDDING_DIMENSIONS: int = 768  # Actual dimensions for Gemini embedding model

    # Jina AI API settings
    JINA_API_KEY: str
    JINA_EMBEDDING_MODEL: str = "jina-embeddings-v3"
    JINA_EMBEDDING_DIMENSIONS: int = 1024  # Actual dimensions for Jina embedding model

    # OpenAI API settings (for reference, not used directly)
    OPENAI_EMBEDDING_DIMENSIONS: int = 1536  # Actual dimensions for OpenAI embedding model

    # Standardized embedding dimensions for vector storage
    STANDARDIZED_EMBEDDING_DIMENSIONS: int = 2048  # All embeddings will be padded/truncated to this size

    # File processing settings
    TEMP_DIR: str = "temp-s3"  # Directory for temporary files
    MAX_FILE_SIZE_MB: int = 20  # Maximum file size in MB
    CHUNK_SIZE: int = 2000  # Default chunk size in characters
    CHUNK_OVERLAP: int = 100  # Default chunk overlap in characters

    # OCR settings
    OCR_ENABLED: bool = True  # Enable OCR processing for image-based PDFs
    OCR_IMAGE_MIN_SIZE: int = 100  # Minimum image size in pixels to process
    OCR_DPI: int = 300  # DPI for PDF to image conversion
    OCR_LANGUAGE: str = "vie+eng"  # Languages for OCR (vie: Vietnamese, eng: English)
    OCR_PAGE_SEGMENTATION_MODE: int = 3  # Tesseract PSM mode (3: auto page segmentation)
    OCR_IMAGE_THRESHOLD: bool = True  # Apply thresholding to improve OCR accuracy

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "extra": "ignore"  # Allow extra fields in .env file
    }


@lru_cache()
def get_settings() -> Settings:
    """Create settings instance with caching."""
    return Settings()
