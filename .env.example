PORT=8000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=redai
DB_SSL=false

# CloudFlare R2
CF_R2_ACCESS_KEY=your_access_key
CF_R2_SECRET_KEY=your_secret_key
CF_R2_ENDPOINT=your_endpoint
CF_BUCKET_NAME=your_bucket_name
CF_R2_REGION=auto

# CDN
CDN_URL=https://your-cdn-url.com
CDN_SECRET_KEY=your_cdn_secret_key

# Google Gemini
GEMINI_API_KEY=your_gemini_api_key

# Jina AI
JINA_API_KEY=your_jina_api_key

# File processing
TEMP_DIR=temp-s3
MAX_FILE_SIZE_MB=100
CHUNK_SIZE=2000
CHUNK_OVERLAP=100

# OCR settings
OCR_ENABLED=true
OCR_IMAGE_MIN_SIZE=100
OCR_DPI=300
OCR_LANGUAGE=vie+eng
OCR_PAGE_SEGMENTATION_MODE=3
OCR_IMAGE_THRESHOLD=true
