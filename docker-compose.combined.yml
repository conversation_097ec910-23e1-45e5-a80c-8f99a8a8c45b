version: '3.8'

services:
  # Service ch<PERSON>y cả server và worker trong một container
  app-combined:
    image: sonpl2603/redai-rag-engine:latest
    ports:
      - "3000:3000"
    env_file:
      - .env
    volumes:
      - ./temp-s3:/app/temp-s3
      - ./queue_data:/app/queue_data
      - ./app_combined.log:/app/app_combined.log
    command: ["python", "run_all.py"]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
