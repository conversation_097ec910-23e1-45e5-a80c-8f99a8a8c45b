#!/bin/bash

echo "Starting Red.ai RAG Engine with Worker..."
echo

# <PERSON><PERSON><PERSON> tra Python
if ! command -v python &> /dev/null; then
    echo "ERROR: Python not found. Please install Python first."
    exit 1
fi

echo "Starting Huey worker in background..."
python run_worker.py &
WORKER_PID=$!

echo "Worker PID: $WORKER_PID"
echo "Waiting 3 seconds for worker to initialize..."
sleep 3

echo "Starting FastAPI server..."
python -m uvicorn main:app --host 0.0.0.0 --port 3000 --reload

# Cleanup: Kill worker when server stops
echo
echo "Stopping worker (PID: $WORKER_PID)..."
kill $WORKER_PID 2>/dev/null

echo "Both services stopped."
