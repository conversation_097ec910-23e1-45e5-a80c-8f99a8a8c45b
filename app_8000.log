2025-05-27 09:03:01,964 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:03:01,964 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:03:01,969 - __main__ - INFO - Worker started with PID: 10788
2025-05-27 09:03:01,969 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:03:04,969 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:03:07,173 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 09:09:38,777 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:09:38,778 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:09:38,783 - __main__ - INFO - Worker started with PID: 14260
2025-05-27 09:09:38,783 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:09:41,783 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:09:44,888 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 09:11:13,604 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:11:13,605 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:11:13,609 - __main__ - INFO - Worker started with PID: 19808
2025-05-27 09:11:13,609 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:11:16,610 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:11:18,674 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 09:18:40,659 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:18:40,659 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:18:40,665 - __main__ - INFO - Worker started with PID: 16544
2025-05-27 09:18:40,665 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:18:43,665 - __main__ - INFO - Testing worker connection...
2025-05-27 09:18:45,385 - __main__ - WARNING - ⚠️ Worker test error: Connection() got an unexpected keyword argument 'blocking', but continuing...
2025-05-27 09:18:45,385 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:18:45,874 - __main__ - ERROR - Error in main: Connection() got an unexpected keyword argument 'blocking'
2025-05-27 09:20:57,145 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:20:57,146 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:20:57,150 - __main__ - INFO - Worker started with PID: 16672
2025-05-27 09:20:57,150 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:21:00,151 - __main__ - INFO - Testing worker connection...
2025-05-27 09:21:01,839 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 09:21:01,839 - services.queue_service - INFO - Testing worker connection...
2025-05-27 09:21:01,840 - services.queue_service - INFO - Test task queued with ID: 6bf269be-faf2-41de-9b37-d290836548cd
2025-05-27 09:21:01,840 - __main__ - INFO - ✅ Worker connection test successful!
2025-05-27 09:21:01,840 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:21:26,850 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:21:26,851 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:21:26,856 - __main__ - INFO - Worker started with PID: 24300
2025-05-27 09:21:26,856 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:21:29,857 - __main__ - INFO - Testing worker connection...
2025-05-27 09:21:31,488 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 09:21:31,488 - services.queue_service - INFO - Testing worker connection...
2025-05-27 09:21:31,489 - services.queue_service - INFO - Test task queued with ID: ab24fc6c-0cb3-4a62-9e3a-d435eccb32ed
2025-05-27 09:21:31,489 - __main__ - INFO - ✅ Worker connection test successful!
2025-05-27 09:21:31,490 - __main__ - INFO - Starting FastAPI server on port 8000...
2025-05-27 09:33:45,314 - __main__ - INFO - Starting Red.ai RAG Engine on PORT 8000...
2025-05-27 09:33:45,315 - __main__ - INFO - Starting Huey worker subprocess...
2025-05-27 09:33:45,320 - __main__ - INFO - Worker started with PID: 16296
2025-05-27 09:33:45,320 - __main__ - INFO - Waiting for worker to initialize...
2025-05-27 09:33:48,320 - __main__ - INFO - Testing worker connection...
2025-05-27 09:33:50,148 - services.queue_service - INFO - Huey queue initialized with SQLite database: queue_data\huey_queue.db
2025-05-27 09:33:50,149 - services.queue_service - INFO - Testing worker connection...
2025-05-27 09:33:50,149 - services.queue_service - INFO - Test task queued with ID: 87c6159e-0a85-471c-8f99-80c9e3b20458
2025-05-27 09:33:50,149 - __main__ - INFO - ✅ Worker connection test successful!
2025-05-27 09:33:50,149 - __main__ - INFO - Starting FastAPI server on port 8000...
