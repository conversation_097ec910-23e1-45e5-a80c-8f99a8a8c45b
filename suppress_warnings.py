"""
Suppress common deprecation warnings to keep logs clean.
This module should be imported early in the application startup.
"""

import warnings
import os

def suppress_common_warnings():
    """Suppress common deprecation warnings that don't affect functionality."""

    # Suppress all websockets deprecation warnings
    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        module="websockets"
    )

    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        module="websockets.legacy"
    )

    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        module="websockets.server"
    )

    # Suppress uvicorn deprecation warnings
    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        module="uvicorn"
    )

    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        module="uvicorn.protocols.websockets.websockets_impl"
    )

    # Suppress specific warning messages
    warnings.filterwarnings(
        "ignore",
        message="websockets.legacy is deprecated*",
        category=DeprecationWarning
    )

    warnings.filterwarnings(
        "ignore",
        message="websockets.server.WebSocketServerProtocol is deprecated*",
        category=DeprecationWarning
    )

    # More aggressive suppression
    warnings.filterwarnings(
        "ignore",
        category=DeprecationWarning,
        message=".*websockets.*"
    )

    # Set environment variable to suppress Python warnings
    os.environ["PYTHONWARNINGS"] = "ignore::DeprecationWarning"

# Auto-suppress warnings when this module is imported
suppress_common_warnings()
