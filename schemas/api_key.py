"""Pydantic schema for API keys."""

from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class ApiKeyBase(BaseModel):
    """Base schema for API keys."""
    name: str = Field(..., description="Tên/mô tả của API key")


class ApiKeyCreate(ApiKeyBase):
    """Schema for creating API keys."""
    expires_at: Optional[int] = Field(None, description="Thời gian hết hạn (milliseconds)")


class ApiKeyRead(ApiKeyBase):
    """Schema for reading API keys."""
    key: str = Field(..., description="API key dạng 'redai-XXXX'")
    created_at: int = Field(..., description="Thời gian tạo (milliseconds)")
    expires_at: Optional[int] = Field(None, description="Thời gian hết hạn (milliseconds)")
    is_active: bool = Field(..., description="Trạng thái hoạt động của API key")

    model_config = ConfigDict(from_attributes=True)  # For SQLAlchemy model compatibility
