"""Pydantic schema for document chunks."""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, UUID4, ConfigDict


class DocumentChunkBase(BaseModel):
    """Base schema for document chunks."""
    file_id: str = Field(..., description="ID của tập tin liên kết")
    chunk_index: int = Field(..., description="Chỉ số của đoạn trong tập tin")
    content: str = Field(..., description="Nội dung của đoạn")
    chunk_metadata: Dict[str, Any] = Field(default={}, description="Thông tin bổ sung về đoạn")


class DocumentChunkCreate(DocumentChunkBase):
    """Schema for creating document chunks."""
    pass


class DocumentChunkRead(DocumentChunkBase):
    """Schema for reading document chunks."""
    id: UUID4 = Field(..., description="ID của đoạn")
    embedding_source: Optional[str] = Field(None, description="Nguồn tạo embedding")
    embedding_dimensions: Optional[int] = Field(None, description="Số chiều của vector embedding")
    created_at: int = Field(..., description="Thời gian tạo (milliseconds)")
    updated_at: int = Field(..., description="Thời gian cập nhật (milliseconds)")

    model_config = ConfigDict(from_attributes=True)  # For SQLAlchemy model compatibility


class DocumentChunkUpdate(BaseModel):
    """Schema for updating document chunks."""
    content: Optional[str] = Field(None, description="Nội dung cập nhật của đoạn")
    chunk_metadata: Optional[Dict[str, Any]] = Field(None, description="Thông tin bổ sung cập nhật")

    model_config = ConfigDict(from_attributes=True)  # For SQLAlchemy model compatibility


class DocumentChunkSearchQuery(BaseModel):
    """Schema for searching document chunks."""
    query: str = Field(..., description="Truy vấn tìm kiếm")
    file_id: Optional[str] = Field(None, description="ID tập tin để lọc kết quả")
    threshold: float = Field(0.7, description="Ngưỡng tương đồng (0-1)")
    limit: int = Field(5, description="Số lượng kết quả tối đa")

    model_config = ConfigDict(
        json_schema_extra={
            "examples": [{
                "query": "Làm thế nào để xử lý tập tin?",
                "file_id": "file-KBYXBHVZX8MMk4BP",
                "threshold": 0.7,
                "limit": 5
            }]
        }
    )
