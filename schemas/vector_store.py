"""Pydantic schemas for vector store operations."""

from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class VectorStoreBase(BaseModel):
    """Base schema for vector store."""
    name: str = Field(..., description="Tên kho vector")


class VectorStoreCreate(VectorStoreBase):
    """Schema for creating a vector store."""
    storage: Optional[int] = Field(default=0, description="Dung lượng đã dùng (bytes/tokens)")


class VectorStoreRead(VectorStoreBase):
    """Schema for reading a vector store."""
    id: str = Field(..., description="ID duy nhất của kho vector")
    storage: int = Field(..., description="Dung lượng đã dùng (bytes/tokens)")
    created_at: int = Field(..., description="Thời điểm tạo (timestamp)")
    update_at: int = Field(..., description="Thời điểm cập nhật (timestamp)")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "vs_681c63f7b8d48191a3394cb3b025b0e7",
                "name": "My Vector Store",
                "storage": 1024,
                "created_at": 1621500000000,
                "update_at": 1621500000000
            }
        }
    )


class VectorStoreUpdate(BaseModel):
    """Schema for updating a vector store."""
    name: Optional[str] = Field(None, description="Tên kho vector")
    storage: Optional[int] = Field(None, description="Dung lượng đã dùng (bytes/tokens)")
    update_at: Optional[int] = Field(None, description="Thời điểm cập nhật (timestamp)")


class VectorStoreFilter(BaseModel):
    """Schema for filtering vector stores."""
    name: Optional[str] = Field(None, description="Tên kho vector (tìm kiếm mờ)")
