"""Pydantic schema for files."""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict


class FileBase(BaseModel):
    """Base schema for files."""
    filename: str = Field(..., description="Tên tập tin")
    filepath: str = Field(..., description="Đường dẫn tập tin trong S3")
    filesize: int = Field(..., description="Kích thước tập tin (byte)")


class FileCreate(FileBase):
    """Schema for creating files."""
    pass


class FileRead(FileBase):
    """Schema for reading files."""
    id: str = Field(..., description="ID tập tin dạng 'file-XXXX'")
    uploaded_at: int = Field(..., description="Thời gian tải lên (milliseconds)")

    model_config = ConfigDict(from_attributes=True)  # For SQLAlchemy model compatibility


class FileUpdate(BaseModel):
    """Schema for updating files."""
    filename: Optional[str] = Field(None, description="<PERSON><PERSON><PERSON> tập tin cập nhật")
    filesize: Optional[int] = Field(None, description="<PERSON><PERSON><PERSON> thước tập tin cập nhật")

    model_config = ConfigDict(from_attributes=True)  # For SQLAlchemy model compatibility
