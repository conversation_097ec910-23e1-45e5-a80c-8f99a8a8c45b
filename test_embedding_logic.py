#!/usr/bin/env python3
"""
Test script for the new intelligent embedding logic.
"""

import logging
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_embedding_logic():
    """Test the new embedding logic."""
    try:
        from core.vector_store import get_embedding_with_source, get_file_embedding_source
        
        logger.info("🧪 Testing new embedding logic...")
        
        # Test 1: Default behavior (should try Gemini first)
        logger.info("\n📝 Test 1: Default behavior (no file context)")
        try:
            embedding, source, dimensions = get_embedding_with_source("This is a test text.")
            logger.info(f"✅ Success: source={source}, dimensions={dimensions}")
        except Exception as e:
            logger.error(f"❌ Failed: {str(e)}")
        
        # Test 2: Preferred source - Jina
        logger.info("\n📝 Test 2: Preferred source - Jina")
        try:
            embedding, source, dimensions = get_embedding_with_source(
                "This is a test text for Jina.", 
                preferred_source="jina"
            )
            logger.info(f"✅ Success: source={source}, dimensions={dimensions}")
        except Exception as e:
            logger.error(f"❌ Failed: {str(e)}")
        
        # Test 3: Preferred source - Gemini
        logger.info("\n📝 Test 3: Preferred source - Gemini")
        try:
            embedding, source, dimensions = get_embedding_with_source(
                "This is a test text for Gemini.", 
                preferred_source="gemini"
            )
            logger.info(f"✅ Success: source={source}, dimensions={dimensions}")
        except Exception as e:
            logger.error(f"❌ Failed: {str(e)}")
        
        # Test 4: File embedding source detection
        logger.info("\n📝 Test 4: File embedding source detection")
        try:
            # Test with non-existent file
            source = get_file_embedding_source("non-existent-file")
            logger.info(f"✅ Non-existent file source: {source}")
            
            # Test with real file (if any exists)
            # This would need a real file ID from database
            
        except Exception as e:
            logger.error(f"❌ Failed: {str(e)}")
        
        logger.info("\n🎉 Embedding logic tests completed!")
        
    except ImportError as e:
        logger.error(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        return False
    
    return True

def test_parallel_embedding():
    """Test parallel embedding functionality."""
    try:
        from core.vector_store import get_embeddings_parallel
        
        logger.info("\n🧪 Testing parallel embedding...")
        
        texts = [
            "This is the first test text.",
            "This is the second test text.",
            "This is the third test text."
        ]
        
        # Test without file_id
        logger.info("\n📝 Test: Parallel embedding without file context")
        try:
            results = get_embeddings_parallel(texts, max_workers=2)
            logger.info(f"✅ Success: Got {len(results)} results")
            for i, (embedding, source, dimensions) in enumerate(results):
                logger.info(f"  Text {i+1}: source={source}, dimensions={dimensions}, embedding_length={len(embedding)}")
        except Exception as e:
            logger.error(f"❌ Failed: {str(e)}")
        
        # Test with file_id
        logger.info("\n📝 Test: Parallel embedding with file context")
        try:
            results = get_embeddings_parallel(texts, max_workers=2, file_id="test-file-123")
            logger.info(f"✅ Success: Got {len(results)} results")
            for i, (embedding, source, dimensions) in enumerate(results):
                logger.info(f"  Text {i+1}: source={source}, dimensions={dimensions}, embedding_length={len(embedding)}")
        except Exception as e:
            logger.error(f"❌ Failed: {str(e)}")
        
        logger.info("\n🎉 Parallel embedding tests completed!")
        
    except ImportError as e:
        logger.error(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        return False
    
    return True

def main():
    """Main test function."""
    logger.info("🚀 Starting embedding logic tests...")
    
    success = True
    
    # Test basic embedding logic
    if not test_embedding_logic():
        success = False
    
    # Test parallel embedding
    if not test_parallel_embedding():
        success = False
    
    if success:
        logger.info("\n✅ All tests passed!")
        return 0
    else:
        logger.error("\n❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
