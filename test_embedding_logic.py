#!/usr/bin/env python3
"""
Test script for the new intelligent embedding logic.
"""

import logging
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_consistency_logic():
    """Test the consistency logic with file context."""
    try:
        from core.vector_store import get_embedding_with_source
        
        logger.info("🧪 Testing embedding consistency logic...")
        
        # Test 1: First embedding for a file (should try Gemini first)
        logger.info("\n📝 Test 1: First embedding for new file")
        try:
            embedding1, source1, dimensions1 = get_embedding_with_source(
                "This is the first chunk of a new file.", 
                file_id="test-file-consistency"
            )
            logger.info(f"✅ First chunk: source={source1}, dimensions={dimensions1}")
        except Exception as e:
            logger.error(f"❌ Failed: {str(e)}")
            return False
        
        # Test 2: Second embedding for same file (should use same source as first)
        logger.info("\n📝 Test 2: Second embedding for same file (should be consistent)")
        try:
            embedding2, source2, dimensions2 = get_embedding_with_source(
                "This is the second chunk of the same file.", 
                file_id="test-file-consistency"
            )
            logger.info(f"✅ Second chunk: source={source2}, dimensions={dimensions2}")
            
            # Check consistency
            if source1 == source2:
                logger.info(f"✅ CONSISTENCY CHECK PASSED: Both chunks use {source1}")
            else:
                logger.error(f"❌ CONSISTENCY CHECK FAILED: First={source1}, Second={source2}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Failed: {str(e)}")
            return False
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"❌ Unexpected error: {str(e)}")
        return False

def test_file_embedding_source():
    """Test the file embedding source detection."""
    try:
        from core.vector_store import get_file_embedding_source
        
        logger.info("\n🧪 Testing file embedding source detection...")
        
        # Test with non-existent file
        logger.info("\n📝 Test: Non-existent file")
        source = get_file_embedding_source("non-existent-file-123")
        logger.info(f"✅ Non-existent file source: {source}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error: {str(e)}")
        return False

def main():
    """Main test function."""
    logger.info("🚀 Starting embedding consistency tests...")
    
    success = True
    
    # Test file embedding source detection
    if not test_file_embedding_source():
        success = False
    
    # Test consistency logic
    if not test_consistency_logic():
        success = False
    
    if success:
        logger.info("\n✅ All consistency tests passed!")
        return 0
    else:
        logger.error("\n❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
