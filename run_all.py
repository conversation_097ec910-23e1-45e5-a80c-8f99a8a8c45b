#!/usr/bin/env python3
"""
Script để chạy cả server và worker trên cổng 8000.

Usage:
    python run_8000.py

Script này sẽ khởi chạy:
1. Huey worker trong subprocess
2. FastAPI server trên cổng 8000
"""

import os
import sys
import subprocess
import time
import signal
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set environment variables for UTF-8 and port
os.environ["PYTHONIOENCODING"] = "utf-8"
os.environ["PYTHONLEGACYWINDOWSSTDIO"] = "0"
os.environ["PORT"] = "8000"  # Force port 8000

# Configure logging - Terminal only
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# Global variables
worker_process = None


def start_worker():
    """Start Huey worker in subprocess."""
    global worker_process

    try:
        logger.info("Starting Huey worker subprocess...")

        # Start worker as subprocess with proper logging
        worker_process = subprocess.Popen(
            [sys.executable, "run_worker.py"],
            stdout=None,  # Let worker output to console
            stderr=None,  # Let worker output to console
            text=True,
            encoding='utf-8'
        )

        logger.info(f"Worker started with PID: {worker_process.pid}")
        return True

    except Exception as e:
        logger.error(f"Error starting worker: {str(e)}")
        return False


def stop_worker():
    """Stop Huey worker subprocess."""
    global worker_process

    if worker_process and worker_process.poll() is None:
        logger.info("Stopping worker subprocess...")
        try:
            worker_process.terminate()
            worker_process.wait(timeout=10)
            logger.info("Worker stopped successfully")
        except subprocess.TimeoutExpired:
            logger.warning("Worker didn't stop gracefully, killing...")
            worker_process.kill()
            worker_process.wait()
        except Exception as e:
            logger.error(f"Error stopping worker: {str(e)}")


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"Received signal {signum}, shutting down...")
    stop_worker()
    logger.info("Shutdown complete")
    sys.exit(0)


def main():
    """Main function."""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        logger.info("Starting Red.ai RAG Engine on PORT 8000...")

        # Start worker
        if not start_worker():
            logger.error("Failed to start worker, exiting...")
            return

        # Give worker time to initialize
        logger.info("Waiting for worker to initialize...")
        time.sleep(3)

        # Test worker connection
        logger.info("Testing worker connection...")
        try:
            from services.queue_service import test_worker_connection
            if test_worker_connection():
                logger.info("✅ Worker connection test successful!")
            else:
                logger.warning("⚠️ Worker connection test failed, but continuing...")
        except Exception as e:
            logger.warning(f"⚠️ Worker test error: {str(e)}, but continuing...")

        # Start server on port 8000
        logger.info("Starting FastAPI server on port 8000...")

        import uvicorn

        # Run server (this will block)
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=8000,  # Fixed port 8000
            reload=False,
            log_level="info"
        )

    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
    finally:
        stop_worker()


if __name__ == "__main__":
    main()
